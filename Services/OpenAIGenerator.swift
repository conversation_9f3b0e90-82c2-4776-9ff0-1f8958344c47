//
//  OpenAIGenerator.swift
//  Siflowtype
//
//  Created by <PERSON><PERSON> on 2024/7/30.
//

import Foundation
import OpenAI
import OSLog

/// OpenAI 候选词生成器
///
/// 负责执行对 AI 的 API 调用，并包含完整的超时和重试逻辑。
/// 实现了健壮的错误处理和自动重试机制，确保服务的可靠性。
class OpenAIGenerator: CandidateGenerator {
    // MARK: - Properties

    private let openAI: OpenAI
    private let logger = Logger(subsystem: "com.siflowtype.app", category: "OpenAIGenerator")

    // MARK: - 默认配置

    private enum DefaultConfig {
        static let temperature: Double = 0.2
        static let timeout: TimeInterval = 30.0
        static let retries: Int = 3
        static let baseRetryDelay: TimeInterval = 1.0
    }

    // MARK: - 初始化

    /// 使用环境配置初始化生成器
    /// - Parameter config: 应用环境配置
    init(config: AppEnvironmentConfiguration) {
        let apiURL = URL(string: config.apiBaseURL)
        openAI = OpenAI(configuration: .init(
            token: config.apiToken,
            host: apiURL?.host ?? "",
            scheme: apiURL?.scheme ?? "https"
        ))

        logger.info("OpenAIGenerator initialized with host: \(apiURL?.host ?? "unknown")")
    }

    /// 使用 OpenAI 客户端直接初始化
    /// - Parameter client: OpenAI 客户端实例
    init(client: OpenAI) {
        openAI = client
        logger.info("OpenAIGenerator initialized with custom client")
    }

    // MARK: - CandidateGenerator 协议实现

    /// 为给定的文本生成结构化响应
    /// - Parameters:
    ///   - text: 用户输入的文本
    ///   - config: 提示词配置
    ///   - temperature: AI 模型的温度参数（0.0-1.0）
    ///   - timeout: 请求超时时间（秒）
    ///   - retries: 最大重试次数
    /// - Returns: 解码后的响应对象
    /// - Throws: 如果生成失败则抛出 GeneratorError
    func generate(
        for text: String,
        with config: PromptConfiguration,
        temperature: Double = DefaultConfig.temperature,
        timeout: TimeInterval = DefaultConfig.timeout,
        retries: Int = DefaultConfig.retries
    ) async throws -> CandidateResponse {
        logger.info("Starting generation for text length: \(text.count), retries: \(retries)")

        // 验证输入
        guard config.isValidInput(text) else {
            throw GeneratorError.inputValidationFailed(reason: "输入文本长度超过限制或为空")
        }

        // 执行重试循环
        var lastError: Error?

        for attempt in 0 ..< max(1, retries + 1) {
            do {
                let result = try await performSingleGeneration(
                    text: text,
                    config: config,
                    temperature: temperature,
                    timeout: timeout
                )

                logger.info("Generation successful on attempt \(attempt + 1)")
                return result

            } catch let error as GeneratorError {
                lastError = error
                logger.warning("Generation attempt \(attempt + 1) failed: \(error.debugDescription)")

                // 如果错误不可重试，直接抛出
                if !error.isRetryable {
                    throw error
                }

                // 如果不是最后一次尝试，等待后重试
                if attempt < retries {
                    let delay = error.suggestedRetryDelay * Double(attempt + 1) // 指数退避
                    logger.info("Retrying after \(delay) seconds...")
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }

            } catch {
                lastError = GeneratorError.networkError(underlyingError: error)
                logger.error("Unexpected error on attempt \(attempt + 1): \(error)")

                if attempt < retries {
                    let delay = DefaultConfig.baseRetryDelay * Double(attempt + 1)
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        // 所有重试都失败了，抛出最后一个错误
        if let generatorError = lastError as? GeneratorError {
            throw generatorError
        } else if let error = lastError {
            throw GeneratorError.networkError(underlyingError: error)
        } else {
            throw GeneratorError.apiError(statusCode: nil, message: "未知错误")
        }
    }

    // MARK: - 私有方法

    /// 执行单次生成请求
    /// - Parameters:
    ///   - text: 用户输入文本
    ///   - config: 提示词配置
    ///   - temperature: 温度参数
    ///   - timeout: 超时时间
    /// - Returns: 解码后的响应
    /// - Throws: GeneratorError
    private func performSingleGeneration(
        text: String,
        config: PromptConfiguration,
        temperature: Double,
        timeout: TimeInterval
    ) async throws -> CandidateResponse {
        // 构建用户提示词
        let userPrompt = config.buildUserPrompt(with: text)

        // 构建查询
        let query = ChatQuery(
            messages: [
                .system(.init(content: .textContent(config.systemPrompt))),
                .user(.init(content: .string(userPrompt))),
            ],
            model: .init(config.model),
            responseFormat: .jsonSchema(
                .init(
                    name: "translation-response",
                    description: nil,
                    schema: .derivedJsonSchema(CandidateResponse.self),
                    strict: true
                )),
            temperature: temperature
        )

        // 执行带超时的请求
        let chatResult = try await self.openAI.chats(query: query)

        // 验证响应内容
        guard let responseContent = chatResult.choices.first?.message.content else {
            throw GeneratorError.noResponseContent(model: config.model)
        }

        // 解析 JSON 响应
        guard let jsonData = responseContent.data(using: .utf8) else {
            throw GeneratorError.responseParsingFailed(
                model: config.model,
                rawResponse: responseContent,
                underlyingError: NSError(domain: "OpenAIGenerator", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to convert response to UTF-8 data"])
            )
        }

        do {
            let decodedResponse = try JSONDecoder().decode(CandidateResponse.self, from: jsonData)
            return decodedResponse
        } catch {
            throw GeneratorError.responseParsingFailed(
                model: config.model,
                rawResponse: responseContent,
                underlyingError: error
            )
        }
    }

    /// 执行带超时的异步操作
    /// - Parameters:
    ///   - timeout: 超时时间（秒）
    ///   - operation: 要执行的异步操作
    /// - Returns: 操作结果
    /// - Throws: GeneratorError.requestTimedOut 或操作本身的错误
    private func withTimeout<R>(_ timeout: TimeInterval, operation: @escaping () async throws -> R) async throws -> R {
        return try await withThrowingTaskGroup(of: R.self) { group in
            // 添加主要操作任务
            group.addTask {
                try await operation()
            }

            // 添加超时任务
            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
                throw GeneratorError.requestTimedOut(after: timeout)
            }

            // 等待第一个完成的任务
            guard let result = try await group.next() else {
                throw GeneratorError.requestTimedOut(after: timeout)
            }

            // 取消其他任务
            group.cancelAll()

            return result
        }
    }
}

// MARK: - 扩展方法

extension OpenAIGenerator {
    /// 便利方法：使用默认参数生成响应
    /// - Parameters:
    ///   - text: 用户输入文本
    ///   - config: 提示词配置
    /// - Returns: 解码后的响应
    func generateWithDefaults(
        for text: String,
        with config: PromptConfiguration
    ) async throws -> CandidateResponse {
        return try await generate(
            for: text,
            with: config,
            temperature: DefaultConfig.temperature,
            timeout: DefaultConfig.timeout,
            retries: DefaultConfig.retries
        )
    }
}
