//
//  CandidateService.swift
//  Siflowtype
//
//  Created by <PERSON><PERSON> on 2024/8/1.
//

import OSLog
import SwiftUI

/// 候选词服务
///
/// 作为门面，编排整个业务流程，管理激活的场景，并执行输入/输出的预处理和后处理。
/// 这是新架构的核心服务，负责协调 PromptProvider 和 CandidateGenerator 的工作。
@MainActor
class CandidateService: ObservableObject {
    // MARK: - Properties

    private let promptProvider: PromptProvider
    private let generator: CandidateGenerator
    private let logger = Logger(subsystem: "com.siflowtype.app", category: "CandidateService")

    /// 当前激活的场景上下文
    @Published var activeScenario: ScenarioContext = .general

    // MARK: - 配置常量

    private enum Config {
        static let defaultTemperature: Double = 0.2
        static let defaultTimeout: TimeInterval = 30.0
        static let defaultRetries: Int = 3
        static let maxInputLength: Int = 1000
        static let minInputLength: Int = 1
    }

    // MARK: - 初始化

    /// 初始化候选词服务
    /// - Parameters:
    ///   - promptProvider: 提示词提供者
    ///   - generator: 候选词生成器
    init(promptProvider: PromptProvider, generator: CandidateGenerator) {
        self.promptProvider = promptProvider
        self.generator = generator

        logger.info("CandidateService initialized with active scenario: \(self.activeScenario.description)")
    }

    // MARK: - 公共方法Ï

    /// 为给定文本获取候选词建议
    /// - Parameter text: 用户输入的文本
    /// - Returns: 建议对数组
    /// - Throws: CandidateServiceError
    func fetchCandidates(for text: String) async throws -> [SuggestionPair] {
        logger.info("Fetching candidates for text length: \(text.count)")

        // 1. 预处理：检查无意义输入
        if isNonsensical(text) {
            logger.warning("Detected nonsensical input, returning original text")
            return [SuggestionPair(completed: text, explanation: text)]
        }

        // 2. 构建当前场景的配置
        let config: PromptConfiguration
        do {
            config = try await promptProvider.buildConfiguration(for: activeScenario)
        } catch {
            logger.error("Failed to build configuration: \(error)")
            throw CandidateServiceError.configurationFailed(underlyingError: error)
        }

        // 3. 验证输入长度
        guard config.isValidInput(text) else {
            logger.warning("Input validation failed for text length: \(text.count)")
            throw CandidateServiceError.inputTooLong(maxLength: config.maxInputLength)
        }

        // 4. 调用生成器
        let response: CandidateResponse
        do {
            response = try await generator.generate(
                for: text,
                with: config,
                temperature: Config.defaultTemperature,
                timeout: Config.defaultTimeout
            )
        } catch let error as GeneratorError {
            logger.error("Generator failed: \(error.debugDescription)")
            throw CandidateServiceError.generationFailed(generatorError: error)
        } catch {
            logger.error("Unexpected generation error: \(error)")
            throw CandidateServiceError.unknownError(underlyingError: error)
        }

        // 5. 后处理：检查 AI 标记的错误响应
        if isErrorResponse(response) {
            logger.warning("AI flagged input as invalid")
            return [SuggestionPair(
                completed: "[无法处理的输入]",
                explanation: "请尝试提供更清晰的内容"
            )]
        }

        // 6. 转换为领域模型并返回
        let suggestions = response.toSuggestionPairs()
        logger.info("Successfully generated \(suggestions.count) suggestions")

        return suggestions
    }

    /// 更新激活的场景
    /// - Parameter scenario: 新的场景上下文
    func updateActiveScenario(_ scenario: ScenarioContext) {
        logger.info("Updating active scenario from '\(self.activeScenario.description)' to '\(scenario.description)'")
        activeScenario = scenario
    }

    // MARK: - 私有方法

    /// 检查输入是否为无意义的随机字符
    /// - Parameter text: 输入文本
    /// - Returns: 是否为无意义输入
    private func isNonsensical(_ text: String) -> Bool {
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // 空文本或过短文本
        if trimmedText.isEmpty || trimmedText.count < Config.minInputLength {
            return true
        }

        // 检查是否为随机字符串（连续的无意义字符）
        let nonsensicalPatterns = [
            "^[a-z]{10,}$", // 连续小写字母
            "^[A-Z]{10,}$", // 连续大写字母
            "^[0-9]{10,}$", // 连续数字
            "^[!@#$%^&*()]{5,}$", // 连续特殊字符
            "^[qwertyuiop]{8,}$", // 键盘连击
            "^[asdfghjkl]{8,}$",
            "^[zxcvbnm]{8,}$",
        ]

        for pattern in nonsensicalPatterns {
            if trimmedText.range(of: pattern, options: .regularExpression) != nil {
                return true
            }
        }

        // 检查字符多样性（如果字符种类太少，可能是无意义输入）
        let uniqueCharacters = Set(trimmedText.lowercased())
        let diversityRatio = Double(uniqueCharacters.count) / Double(trimmedText.count)

        if trimmedText.count > 20 && diversityRatio < 0.3 {
            return true
        }

        return false
    }

    /// 检查 AI 响应是否标记为错误
    /// - Parameter response: AI 响应
    /// - Returns: 是否为错误响应
    private func isErrorResponse(_ response: CandidateResponse) -> Bool {
        // 检查是否包含 AI 标记的无效输入标识
        for suggestion in response.suggestions {
            if suggestion.completed.contains("[INVALID_INPUT]") {
                return true
            }
        }

        // 检查是否所有建议都相同（可能表示处理失败）
        if response.suggestions.count > 1 {
            let firstCompleted = response.suggestions.first?.completed ?? ""
            let allSame = response.suggestions.allSatisfy { $0.completed == firstCompleted }
            if allSame && !firstCompleted.isEmpty {
                return true
            }
        }

        return false
    }
}

// MARK: - 错误类型

/// 候选词服务错误
enum CandidateServiceError: Error, LocalizedError {
    case inputTooLong(maxLength: Int)
    case inputTooShort(minLength: Int)
    case configurationFailed(underlyingError: Error)
    case generationFailed(generatorError: GeneratorError)
    case unknownError(underlyingError: Error)

    var errorDescription: String? {
        switch self {
        case let .inputTooLong(maxLength):
            return "输入文本过长，最大长度为 \(maxLength) 个字符。"
        case let .inputTooShort(minLength):
            return "输入文本过短，最小长度为 \(minLength) 个字符。"
        case .configurationFailed:
            return "服务配置失败，请重试。"
        case let .generationFailed(generatorError):
            return generatorError.errorDescription
        case .unknownError:
            return "发生未知错误，请重试。"
        }
    }

    var failureReason: String? {
        switch self {
        case .inputTooLong:
            return "输入长度超出限制"
        case .inputTooShort:
            return "输入长度不足"
        case .configurationFailed:
            return "配置构建失败"
        case .generationFailed:
            return "AI 生成失败"
        case .unknownError:
            return "未知错误"
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case let .inputTooLong(maxLength):
            return "请将输入文本缩短至 \(maxLength) 个字符以内。"
        case let .inputTooShort(minLength):
            return "请提供至少 \(minLength) 个字符的输入。"
        case .configurationFailed:
            return "请检查网络连接并重试。"
        case let .generationFailed(generatorError):
            return generatorError.recoverySuggestion
        case .unknownError:
            return "请重启应用或联系技术支持。"
        }
    }
}
